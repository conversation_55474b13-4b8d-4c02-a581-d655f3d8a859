<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Vehicles - MOTIVUS Owner Dashboard</title>
    <link href="<?php echo e(asset('css/motivus.css')); ?>" rel="stylesheet">
    <style>
        .owner-dashboard {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .dashboard-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #0099cc;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .actions-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .vehicles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .vehicle-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .vehicle-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .vehicle-image {
            width: 100%;
            height: 180px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #999;
            position: relative;
        }
        
        .vehicle-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-unavailable {
            background: #f8d7da;
            color: #721c24;
        }
        
        .vehicle-info {
            padding: 20px;
        }
        
        .vehicle-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .vehicle-details {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        
        .vehicle-price {
            font-size: 1.4rem;
            font-weight: 700;
            color: #0099cc;
            margin-bottom: 15px;
        }
        
        .vehicle-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .vehicle-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-edit {
            flex: 1;
            background: #28a745;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-edit:hover {
            background: #218838;
            color: white;
            text-decoration: none;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .no-vehicles {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-vehicles-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .dashboard-title {
                font-size: 2rem;
            }
            
            .vehicles-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-bar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Desktop Navigation -->
    <nav class="desktop-nav">
        <div class="nav-container">
            <a href="/" class="nav-logo">MOTIVUS</a>
            <div class="nav-links">
                <a href="<?php echo e(route('vehicles.index')); ?>" class="nav-link">Browse Cars</a>
                <?php if(auth()->guard()->check()): ?>
                    <?php if(auth()->user()->isRenter()): ?>
                        <a href="<?php echo e(route('bookings.index')); ?>" class="nav-link">My Bookings</a>
                    <?php endif; ?>
                    <?php if(auth()->user()->isOwner()): ?>
                        <a href="<?php echo e(route('owner.vehicles.index')); ?>" class="nav-link active">My Vehicles</a>
                        <a href="<?php echo e(route('owner.bookings.index')); ?>" class="nav-link">Booking Requests</a>
                    <?php endif; ?>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="nav-link" style="background: none; border: none; color: white;">Logout</button>
                    </form>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" class="nav-btn">Sign In</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="owner-dashboard">
        <div class="dashboard-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h1 class="dashboard-title">Welcome, <?php echo e(auth()->user()->name); ?>!</h1>
                <p class="dashboard-subtitle">Manage your vehicle listings and track your rental business</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($vehicles->total()); ?></div>
                    <div class="stat-label">Total Vehicles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($vehicles->where('availability', true)->count()); ?></div>
                    <div class="stat-label">Available</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo e($vehicles->sum('total_bookings')); ?></div>
                    <div class="stat-label">Total Bookings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">KSh <?php echo e(number_format($vehicles->sum('price_per_day'))); ?></div>
                    <div class="stat-label">Daily Revenue Potential</div>
                </div>
            </div>

            <!-- Actions Bar -->
            <div class="actions-bar">
                <h2 style="margin: 0; color: #333;">My Vehicle Listings</h2>
                <a href="<?php echo e(route('owner.vehicles.create')); ?>" class="primary-btn">
                    + Add New Vehicle
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if(session('success')): ?>
                <div class="alert alert-success" style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="alert alert-danger" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div><?php echo e($error); ?></div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>

            <!-- Vehicles Grid -->
            <?php if($vehicles->count() > 0): ?>
                <div class="vehicles-grid">
                    <?php $__currentLoopData = $vehicles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="vehicle-card">
                            <div class="vehicle-image">
                                <?php if($vehicle->image_url): ?>
                                    <img src="<?php echo e($vehicle->image_url); ?>" alt="<?php echo e($vehicle->full_name); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                                <?php else: ?>
                                    🚗
                                <?php endif; ?>
                                <div class="vehicle-status <?php echo e($vehicle->availability ? 'status-available' : 'status-unavailable'); ?>">
                                    <?php echo e($vehicle->availability ? 'Available' : 'Unavailable'); ?>

                                </div>
                            </div>
                            <div class="vehicle-info">
                                <h3 class="vehicle-name"><?php echo e($vehicle->full_name); ?></h3>
                                <div class="vehicle-details">📍 <?php echo e($vehicle->location); ?></div>
                                <div class="vehicle-price">KSh <?php echo e(number_format($vehicle->price_per_day)); ?>/day</div>
                                <div class="vehicle-stats">
                                    <span><?php echo e($vehicle->total_bookings); ?> bookings</span>
                                    <span>Listed <?php echo e($vehicle->created_at->diffForHumans()); ?></span>
                                </div>
                                <div class="vehicle-actions">
                                    <a href="<?php echo e(route('owner.vehicles.edit', $vehicle)); ?>" class="btn-edit">
                                        Edit Vehicle
                                    </a>
                                    <form method="POST" action="<?php echo e(route('owner.vehicles.destroy', $vehicle)); ?>" style="display: inline;" 
                                          onsubmit="return confirm('Are you sure you want to delete this vehicle?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn-delete">🗑️</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div style="display: flex; justify-content: center; margin-top: 40px;">
                    <?php echo e($vehicles->links()); ?>

                </div>
            <?php else: ?>
                <div class="no-vehicles">
                    <div class="no-vehicles-icon">🚗</div>
                    <h3>No vehicles listed yet</h3>
                    <p>Start earning by listing your first vehicle!</p>
                    <a href="<?php echo e(route('owner.vehicles.create')); ?>" class="primary-btn" style="margin-top: 20px;">
                        List Your First Vehicle
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\MOTIVUS\resources\views/owner/vehicles/index.blade.php ENDPATH**/ ?>